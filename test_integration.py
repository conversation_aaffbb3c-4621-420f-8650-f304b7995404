#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文字识别集成到物体分析的功能
"""

import sys
import os

def test_imports():
    """测试导入是否正常"""
    print("🧪 测试模块导入...")
    
    try:
        from object_analyzer import ObjectAnalyzer, ObjectAnalysisResult
        print("✅ ObjectAnalyzer导入成功")
        
        from affine_mapper import AffineMapper
        print("✅ AffineMapper导入成功")
        
        from text_recognizer import TextRecognizer
        print("✅ TextRecognizer导入成功")
        
        from coordinate_transformer import CoordinateTransformer
        print("✅ CoordinateTransformer导入成功")
        
        from camera_controller import CameraController
        print("✅ CameraController导入成功")
        
        from config import default_config
        print("✅ Config导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_object_analyzer_init():
    """测试ObjectAnalyzer初始化"""
    print("\n🧪 测试ObjectAnalyzer初始化...")
    
    try:
        from object_analyzer import ObjectAnalyzer
        from coordinate_transformer import CoordinateTransformer
        from config import default_config
        
        # 创建模拟的坐标转换器
        coord_transformer = CoordinateTransformer(default_config)
        
        # 测试不带文字识别组件的初始化
        analyzer1 = ObjectAnalyzer(coord_transformer, default_config)
        print("✅ 基本初始化成功")
        
        # 测试带文字识别组件的初始化（模拟）
        analyzer2 = ObjectAnalyzer(
            coord_transformer, 
            default_config,
            text_recognizer=None,  # 模拟
            affine_mapper=None,    # 模拟
            camera_controller=None # 模拟
        )
        print("✅ 带文字识别组件的初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ ObjectAnalyzer初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_affine_mapper():
    """测试仿射变换映射器"""
    print("\n🧪 测试AffineMapper...")
    
    try:
        from affine_mapper import AffineMapper
        
        # 检查标定文件是否存在
        calibration_file = "output/models/affine_calibration_results.json"
        if not os.path.exists(calibration_file):
            print(f"⚠️ 标定文件不存在: {calibration_file}")
            return False
        
        # 初始化映射器
        mapper = AffineMapper(calibration_file)
        
        if mapper.is_calibration_loaded():
            print("✅ 仿射变换标定数据加载成功")
            
            # 测试点变换
            test_point = (320, 240)  # 640×480中心点
            transformed_point = mapper.transform_point_640_to_1920(*test_point)
            print(f"✅ 点变换测试: {test_point} → {transformed_point}")
            
            return True
        else:
            print("❌ 仿射变换标定数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ AffineMapper测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始集成测试...")
    
    tests = [
        ("模块导入", test_imports),
        ("ObjectAnalyzer初始化", test_object_analyzer_init),
        ("AffineMapper功能", test_affine_mapper),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
