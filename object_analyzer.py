#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
物体分析模块
分析检测到的物体是否为真实物体还是平面图片
基于桌面坐标系中Z值（高度）的方差进行判断
"""

import numpy as np
from typing import List, Tuple, Optional, Dict, Any
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import open3d as o3d
from dataclasses import dataclass
import cv2 # Added for mask erosion

from config import SystemConfig, PathConfig
from yolo_detector import Detection
from coordinate_transformer import CoordinateTransformer


@dataclass
class ObjectAnalysisResult:
    """物体分析结果类"""
    
    def __init__(self, detection=None):
        """
        初始化物体分析结果
        
        Args:
            detection: 检测结果对象
        """
        # 检测信息
        self.detection = detection
        self.class_name = detection.class_name if detection else None
        self.confidence = detection.confidence if detection else 0.0
        self.bbox = detection.bbox if detection else None
        self.mask = detection.mask if detection else None
        
        # 3D点云数据
        self.camera_points = None  # 相机坐标系下的3D点
        self.desktop_points = None  # 桌面坐标系下的3D点
        
        # 分析结果
        self.is_real_object = False  # 是否为真实物体
        self.classification = "未知"  # 分类结果
        self.classification_confidence = 0.0  # 分类置信度

        # 统计信息
        self.point_count = 0  # 点云数量
        self.height_variance = 0.0  # 高度方差
        self.height_range = (0.0, 0.0)  # 高度范围
        self.average_height = 0.0  # 平均高度

        # 兼容旧版本的属性
        self.points_3d_camera = None  # 相机坐标系下的3D点
        self.points_3d_desktop = None  # 桌面坐标系下的3D点

    def __str__(self):
        return f"ObjectAnalysisResult(class_name={self.class_name}, is_real_object={self.is_real_object}, confidence={self.confidence}, height_variance={self.height_variance}, point_count={self.point_count})"


class ObjectAnalyzer:
    """
    物体分析器
    分析检测到的物体是否为真实物体还是平面图片
    """
    
    def __init__(self, coordinate_transformer: CoordinateTransformer, config: SystemConfig = None,
                 text_recognizer=None, affine_mapper=None, camera_controller=None):
        """
        初始化物体分析器

        Args:
            coordinate_transformer: 坐标转换器
            config: 系统配置
            text_recognizer: 文字识别器（可选）
            affine_mapper: 仿射变换映射器（可选）
            camera_controller: 相机控制器（可选）
        """
        if config is None:
            from config import default_config
            config = default_config

        self.config = config
        self.analysis_config = config.object_analysis
        self.coordinate_transformer = coordinate_transformer

        # 文字识别相关组件
        self.text_recognizer = text_recognizer
        self.affine_mapper = affine_mapper
        self.camera_controller = camera_controller

        print(f"🔍 初始化物体分析器...")
        print(f"   最小点数: {self.analysis_config.min_points_threshold}")
        print(f"   高度范围 (参考): {self.analysis_config.min_height_threshold}~{self.analysis_config.max_height_threshold}mm")

        # 检查文字识别组件状态
        if self.text_recognizer and self.affine_mapper and self.camera_controller:
            print(f"   📝 文字识别组件已集成，将在book类真实物体分析时自动进行文字识别")
        else:
            print(f"   ⚠️ 文字识别组件未完全集成，将跳过文字识别")
        print(f"   掩膜腐蚀处理: {'启用' if self.analysis_config.use_mask_erosion else '禁用'}")
        if self.analysis_config.use_mask_erosion:
            print(f"     腐蚀核大小: {self.analysis_config.mask_erosion_size}, 迭代次数: {self.analysis_config.mask_erosion_iterations}")
        print(f"   异常值过滤: {'启用' if self.analysis_config.enable_outlier_filtering else '禁用'}")
        if self.analysis_config.enable_outlier_filtering:
            print(f"     检测方法: {self.analysis_config.outlier_detection_method}")
            print(f"     警告阈值: {self.analysis_config.outlier_filtering_threshold:.1%}")
    
    def _erode_mask_boundary(self, mask: np.ndarray) -> np.ndarray:
        """
        对掩膜边界进行轻微腐蚀，减少边界误差
        
        Args:
            mask: 输入掩膜
            
        Returns:
            腐蚀后的掩膜
        """
        if mask is None or mask.ndim != 2:
            return mask
        
        try:
            # 使用更小的腐蚀核，减少偏移
            kernel_size = 1  # 从2减少到1
            iterations = 1
            
            # 创建腐蚀核
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
            
            # 应用腐蚀
            eroded_mask = cv2.erode(mask.astype(np.uint8), kernel, iterations=iterations)
            
            # 验证腐蚀效果
            original_area = np.sum(mask)
            eroded_area = np.sum(eroded_mask)
            erosion_ratio = eroded_area / original_area if original_area > 0 else 0
            
            print(f"     腐蚀处理: 核大小={kernel_size}, 迭代={iterations}")
            print(f"     面积变化: {original_area} → {eroded_area} (比例: {erosion_ratio:.3f})")
            
            # 如果腐蚀过度，返回原掩膜
            if erosion_ratio < 0.7:
                print(f"     ⚠️ 腐蚀过度，返回原掩膜")
                return mask
            
            return eroded_mask.astype(bool)
            
        except Exception as e:
            print(f"     ⚠️ 掩膜腐蚀失败: {e}")
            return mask

    def _get_pixels_from_detection(
        self, 
        detection: Detection, 
        image_shape: Tuple[int, int], 
        strategy: str = "mask"
    ) -> List[Tuple[int, int]]:
        """
        根据策略从检测结果中获取像素点
        
        Args:
            detection: YOLO检测结果
            image_shape: 图像形状 (height, width)
            strategy: 像素提取策略 ("mask" 或 "bbox")
            
        Returns:
            像素坐标列表 [(u, v), ...]
        """
        if strategy == "mask" and detection.mask is not None:
            # 验证掩膜质量
            self._validate_mask_quality(detection.mask, detection.bbox, image_shape)
            return self._get_pixels_in_mask(detection.mask)
        
        # Fallback to bounding box if mask is not available or strategy is different
        x1, y1, x2, y2 = map(int, detection.bbox)
        
        # 确保边界框在图像范围内
        x1 = max(0, x1)
        y1 = max(0, y1)
        x2 = min(image_shape[1], x2)
        y2 = min(image_shape[0], y2)
        
        pixels = []
        for v in range(y1, y2):
            for u in range(x1, x2):
                pixels.append((u, v))
        return pixels
    
    def _validate_mask_quality(self, mask: np.ndarray, bbox: np.ndarray, image_shape: Tuple[int, int]):
        """
        验证掩膜质量，诊断可能的偏移问题
        
        Args:
            mask: 掩膜数组
            bbox: 边界框 [x1, y1, x2, y2]
            image_shape: 图像形状 (height, width)
        """
        try:
            if mask is None:
                return
            
            mask_height, mask_width = mask.shape
            img_height, img_width = image_shape
            
            print(f"   🔍 掩膜质量验证:")
            print(f"     掩膜尺寸: {mask_width}x{mask_height}")
            print(f"     图像尺寸: {img_width}x{img_height}")
            print(f"     边界框: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]")
            
            # 检查掩膜尺寸是否与图像匹配
            if mask_width != img_width or mask_height != img_height:
                print(f"     ⚠️ 掩膜尺寸与图像不匹配！")
                print(f"       宽度差异: {abs(mask_width - img_width)}")
                print(f"       高度差异: {abs(mask_height - img_height)}")
            
            # 检查边界框是否在掩膜范围内
            x1, y1, x2, y2 = bbox
            if x1 < 0 or y1 < 0 or x2 > mask_width or y2 > mask_height:
                print(f"     ⚠️ 边界框超出掩膜范围！")
                print(f"       边界框: [{x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}]")
                print(f"       掩膜范围: [0, 0, {mask_width}, {mask_height}]")
            
            # 检查掩膜中有效像素的数量
            valid_pixels = np.sum(mask > 0)
            bbox_area = (x2 - x1) * (y2 - y1)
            coverage_ratio = valid_pixels / bbox_area if bbox_area > 0 else 0
            
            print(f"     掩膜覆盖率: {valid_pixels}/{bbox_area:.0f} = {coverage_ratio:.3f}")
            
            if coverage_ratio < 0.3:
                print(f"     ⚠️ 掩膜覆盖率过低，可能存在偏移问题")
            
        except Exception as e:
            print(f"     ⚠️ 掩膜质量验证失败: {e}")

    def _get_pixels_in_mask(self, mask: np.ndarray) -> List[Tuple[int, int]]:
        """
        从二值掩码中获取所有非零像素的坐标，包含腐蚀处理
        使用均匀网格采样策略，确保高度统计更稳定
        
        Args:
            mask: 二值掩膜 (height, width)
            
        Returns:
            像素坐标列表 [(u, v), ...]
        """
        if mask is None or mask.ndim != 2:
            return []
        
        # 检查是否需要腐蚀处理
        processed_mask = mask
        if self.config.object_analysis.use_mask_erosion:
            processed_mask = self._erode_mask_boundary(mask)
        
        # 获取非零像素坐标
        pixels_v, pixels_u = np.where(processed_mask > 0)
        
        if len(pixels_u) == 0:
            print(f"   ⚠️ 掩膜中没有有效像素点")
            return []
        
        # 限制采样数量，避免计算量过大
        max_samples = 20000  # 增加采样数量以获得更好的精度
        
        if len(pixels_u) > max_samples:
            # 使用均匀网格采样而不是随机采样，保证高度统计更稳定
            step = int(np.sqrt(len(pixels_u) / max_samples))
            if step < 1:
                step = 1
            
            # 确保step是合理的，避免采样点数过少
            step = max(1, min(step, len(pixels_u) // 100))  # 至少保证100个采样点
            
            # 网格采样：每隔step个像素取一个
            sampled_indices = np.arange(0, len(pixels_u), step)
            pixels_u = pixels_u[sampled_indices]
            pixels_v = pixels_v[sampled_indices]
            
            print(f"   📏 使用均匀网格采样，step={step}，采样点数: {len(pixels_u)}")
        else:
            print(f"   📏 像素点数({len(pixels_u)})未超过阈值，使用全部像素点")
        
        # 验证像素坐标的有效性
        mask_height, mask_width = mask.shape
        valid_pixels = []
        for u, v in zip(pixels_u, pixels_v):
            if 0 <= u < mask_width and 0 <= v < mask_height:
                valid_pixels.append((u, v))
            else:
                print(f"   ⚠️ 发现无效像素坐标: ({u}, {v}), 掩膜尺寸: {mask_width}x{mask_height}")
        
        if len(valid_pixels) != len(pixels_u):
            print(f"   ⚠️ 像素坐标验证: {len(pixels_u)} → {len(valid_pixels)} 个有效坐标")
        
        return valid_pixels

    def analyze_objects_batch(
        self,
        frames: any,
        detections: List[Detection],
        image_shape: Tuple[int, int],
        strategy: str = "mask",  # Default to using mask
        high_res_image: np.ndarray = None  # 高分辨率图像用于文字识别
    ) -> List[ObjectAnalysisResult]:
        """
        批量分析多个物体
        
        Args:
            frames: 相机帧数据
            detections: YOLO检测结果列表
            image_shape: 图像尺寸 (height, width)
            strategy: 像素提取策略
            
        Returns:
            物体分析结果列表
        """
        if not detections:
            print("⚠️ 没有检测到物体，返回空结果")
            return []
        
        print(f"🔍 开始分析 {len(detections)} 个检测物体...")
        
        # 获取平面检测信息
        plane_info = self.coordinate_transformer.get_plane_detection_info()
        
        # 不进行任何过滤，直接分析所有检测到的物体
        print("📋 分析所有检测到的物体（不进行桌面过滤）...")
        filtered_detections = detections
        print(f"   总检测数量: {len(filtered_detections)}")
        
        # 分析每个物体
        results = []
        for i, detection in enumerate(filtered_detections):
            print(f"\n🔍 分析第 {i+1}/{len(filtered_detections)} 个检测物体: {detection.class_name}")
            
            # 使用新的像素获取方法（包含掩膜腐蚀）
            pixels = self._get_pixels_from_detection(detection, image_shape, strategy)
            
            if not pixels:
                print(f"   - ⚠️ 警告: 目标 {detection.class_name} 中没有可分析的像素点。")
                continue
            
            print(f"   📏 获取到 {len(pixels)} 个有效像素点用于分析")
            
            # 批量转换像素到3D点（图像坐标→相机坐标→桌面坐标）
            print(f"   🔄 批量转换像素到桌面坐标系...")
            camera_points, desktop_points = self.coordinate_transformer.batch_transform_pixels_to_3d(
                pixels, frames
            )
            
            # 进行分类
            result = self._classify_object(
                detection,
                camera_points,
                desktop_points,
                i,
                high_res_image  # 传递高分辨率图像
            )
            
            if result:
                results.append(result)
        
        print(f"\n✅ 批量分析完成，成功分析 {len(results)} 个物体")
        
        return results

    def _debug_print_stats(self, heights: np.ndarray, table_height: float, height_variance: float,
                          average_height: float, height_range: float, point_count: int):
        """
        打印调试统计信息

        Args:
            heights: 高度数组
            table_height: 桌面高度
            height_variance: 高度方差
            average_height: 平均高度
            height_range: 高度范围
            point_count: 点数
        """
        print(f"   📊 桌面坐标系高度分析:")
        print(f"     高度范围(毫米): {np.min(heights):.1f} ~ {np.max(heights):.1f}")
        print(f"     前5个高度值(毫米): {heights[:5]}")
        print(f"     平均高度(毫米): {average_height:.1f}")
        print(f"   📋 高度统计(毫米) - 平均:{average_height:.1f}mm, 范围:{height_range:.1f}mm")
        print(f"   📊 基于物体平均高度的方差: {height_variance:.1f}mm² (基准高度: {table_height:.1f}mm)")
        print(f"   📈 有效点云数: {point_count}")



    def _filter_outliers_statistical(self, heights: np.ndarray, method: str = "iqr") -> Tuple[np.ndarray, np.ndarray]:
        """
        使用统计学方法过滤异常高度值
        
        Args:
            heights: 高度数组
            method: 异常值检测方法 ("iqr", "zscore", "modified_zscore")
            
        Returns:
            (过滤后的高度数组, 异常值索引数组)
        """
        if len(heights) < 10:  # 点数太少，不进行异常值过滤
            return heights, np.array([])
        
        print(f"   🔍 异常值检测 (方法: {method}):")
        print(f"     原始点数: {len(heights)}")
        print(f"     原始高度范围: {np.min(heights):.1f} ~ {np.max(heights):.1f}mm")
        
        outliers_mask = np.zeros(len(heights), dtype=bool)
        
        if method == "iqr":
            # 四分位距方法 (IQR)
            Q1 = np.percentile(heights, 25)
            Q3 = np.percentile(heights, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers_mask = (heights < lower_bound) | (heights > upper_bound)
            
            print(f"     Q1: {Q1:.1f}mm, Q3: {Q3:.1f}mm, IQR: {IQR:.1f}mm")
            print(f"     下界: {lower_bound:.1f}mm, 上界: {upper_bound:.1f}mm")
            
        elif method == "zscore":
            # Z-score方法
            mean_height = np.mean(heights)
            std_height = np.std(heights)
            z_scores = np.abs((heights - mean_height) / std_height)
            outliers_mask = z_scores > 2.5  # 2.5个标准差
            
            print(f"     均值: {mean_height:.1f}mm, 标准差: {std_height:.1f}mm")
            print(f"     Z-score阈值: 2.5")
            
        elif method == "modified_zscore":
            # 修正Z-score方法 (对异常值更鲁棒)
            median_height = np.median(heights)
            mad = np.median(np.abs(heights - median_height))  # 中位数绝对偏差
            if mad > 0:
                modified_z_scores = np.abs(0.6745 * (heights - median_height) / mad)
                outliers_mask = modified_z_scores > 3.5  # 3.5个修正Z-score
            else:
                outliers_mask = np.zeros(len(heights), dtype=bool)
            
            print(f"     中位数: {median_height:.1f}mm, MAD: {mad:.1f}mm")
            print(f"     修正Z-score阈值: 3.5")
        
        # 统计异常值
        outlier_count = np.sum(outliers_mask)
        outlier_ratio = outlier_count / len(heights)
        
        print(f"     检测到异常值: {outlier_count} 个 ({outlier_ratio:.1%})")
        
        if outlier_count > 0:
            outlier_heights = heights[outliers_mask]
            print(f"     异常值范围: {np.min(outlier_heights):.1f} ~ {np.max(outlier_heights):.1f}mm")
            
            # 如果异常值比例过高，可能需要调整检测参数
            if outlier_ratio > self.analysis_config.outlier_filtering_threshold:
                print(f"     ⚠️ 异常值比例过高 ({outlier_ratio:.1%} > {self.analysis_config.outlier_filtering_threshold:.1%})，可能存在掩膜偏移问题")
        
        # 返回过滤后的高度和异常值索引
        filtered_heights = heights[~outliers_mask]
        outlier_indices = np.where(outliers_mask)[0]
        
        # 验证异常值索引的有效性
        if len(outlier_indices) > 0:
            # 确保索引在有效范围内
            valid_outlier_indices = outlier_indices[outlier_indices < len(heights)]
            if len(valid_outlier_indices) != len(outlier_indices):
                print(f"     ⚠️ 发现无效索引，已过滤: {len(outlier_indices)} → {len(valid_outlier_indices)}")
                outlier_indices = valid_outlier_indices
        
        # 额外检查：针对掩膜偏移的特殊检测
        if len(filtered_heights) > 0 and outlier_count > 0:
            self._check_mask_offset_pattern(heights, outliers_mask)
        
        return filtered_heights, outlier_indices
    
    def _calculate_fuzzy_score(self, text: str, keywords: set) -> float:
        """
        计算文本与关键词集合的模糊匹配分数
        
        Args:
            text: 待匹配的文本
            keywords: 关键词集合
            
        Returns:
            模糊匹配分数 (0.0 - 1.0)
        """
        if not text or not keywords:
            return 0.0
        
        total_score = 0.0
        best_matches = []
        
        # 将文本按空格分割成单词
        text_words = text.split()
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            best_word_score = 0.0
            
            # 对每个关键词，找到文本中最相似的单词
            for word in text_words:
                if len(word) < 2:  # 跳过太短的单词
                    continue
                
                # 计算编辑距离相似度
                similarity = self._calculate_edit_similarity(word, keyword_lower)
                
                # 计算包含关系相似度
                contains_score = self._calculate_contains_similarity(word, keyword_lower)
                
                # 计算字符重叠相似度
                overlap_score = self._calculate_overlap_similarity(word, keyword_lower)
                
                # 综合相似度分数
                word_score = max(similarity, contains_score, overlap_score)
                best_word_score = max(best_word_score, word_score)
            
            if best_word_score > 0.3:  # 相似度阈值
                total_score += best_word_score
                best_matches.append((keyword, best_word_score))
        
        # 返回平均分数
        return total_score / len(keywords) if keywords else 0.0
    
    def _calculate_edit_similarity(self, word1: str, word2: str) -> float:
        """
        计算两个单词的编辑距离相似度
        
        Args:
            word1: 第一个单词
            word2: 第二个单词
            
        Returns:
            相似度分数 (0.0 - 1.0)
        """
        if word1 == word2:
            return 1.0
        
        # 计算编辑距离
        len1, len2 = len(word1), len(word2)
        if len1 == 0:
            return 0.0
        if len2 == 0:
            return 0.0
        
        # 使用动态规划计算编辑距离
        dp = [[0] * (len2 + 1) for _ in range(len1 + 1)]
        
        for i in range(len1 + 1):
            dp[i][0] = i
        for j in range(len2 + 1):
            dp[0][j] = j
        
        for i in range(1, len1 + 1):
            for j in range(1, len2 + 1):
                if word1[i-1] == word2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1]) + 1
        
        # 计算相似度
        max_len = max(len1, len2)
        similarity = 1.0 - (dp[len1][len2] / max_len)
        return max(0.0, similarity)
    
    def _calculate_contains_similarity(self, word: str, keyword: str) -> float:
        """
        计算包含关系相似度
        
        Args:
            word: 待匹配的单词
            keyword: 关键词
            
        Returns:
            相似度分数 (0.0 - 1.0)
        """
        if keyword in word:
            return 1.0
        if word in keyword:
            return len(word) / len(keyword)
        
        # 检查部分包含
        for i in range(len(keyword) - 1):
            for j in range(i + 2, len(keyword) + 1):
                substring = keyword[i:j]
                if substring in word and len(substring) >= 2:
                    return len(substring) / len(keyword)
        
        return 0.0
    
    def _calculate_overlap_similarity(self, word: str, keyword: str) -> float:
        """
        计算字符重叠相似度
        
        Args:
            word: 待匹配的单词
            keyword: 关键词
            
        Returns:
            相似度分数 (0.0 - 1.0)
        """
        if not word or not keyword:
            return 0.0
        
        # 计算公共字符数量
        common_chars = set(word) & set(keyword)
        total_chars = set(word) | set(keyword)
        
        if not total_chars:
            return 0.0
        
        return len(common_chars) / len(total_chars)
    
    def _check_mask_offset_pattern(self, heights: np.ndarray, outliers_mask: np.ndarray):
        """
        检查是否存在掩膜偏移的典型模式
        
        Args:
            heights: 高度数组
            outliers_mask: 异常值掩膜
        """
        try:
            if len(heights) < 20 or np.sum(outliers_mask) < 5:
                return
            
            # 分析异常值的分布模式
            normal_heights = heights[~outliers_mask]
            outlier_heights = heights[outliers_mask]
            
            if len(normal_heights) == 0 or len(outlier_heights) == 0:
                return
            
            # 计算正常值和异常值的统计特征
            normal_mean = np.mean(normal_heights)
            normal_std = np.std(normal_heights)
            outlier_mean = np.mean(outlier_heights)
            
            # 检查是否存在明显的双峰分布（掩膜偏移的典型特征）
            height_diff = abs(outlier_mean - normal_mean)
            
            print(f"     🔍 掩膜偏移模式检测:")
            print(f"       正常值均值: {normal_mean:.1f}mm ± {normal_std:.1f}mm")
            print(f"       异常值均值: {outlier_mean:.1f}mm")
            print(f"       高度差异: {height_diff:.1f}mm")
            
            # 如果高度差异很大且异常值比例适中，可能是掩膜偏移
            if height_diff > 20.0 and 0.05 < np.sum(outliers_mask) / len(heights) < 0.4:
                print(f"       🎯 检测到可能的掩膜偏移模式:")
                print(f"         - 高度差异大 ({height_diff:.1f}mm > 20mm)")
                print(f"         - 异常值比例适中 ({np.sum(outliers_mask) / len(heights):.1%})")
                print(f"         - 建议检查掩膜质量和边界处理")
            
            # 检查异常值是否集中在特定高度范围
            outlier_range = np.max(outlier_heights) - np.min(outlier_heights)
            if outlier_range < normal_std * 2:
                print(f"       📊 异常值高度范围较小 ({outlier_range:.1f}mm)，可能来自同一平面")
            
        except Exception as e:
            print(f"       ⚠️ 掩膜偏移模式检测失败: {e}")

    def _classify_object(
        self,
        detection: Detection,
        world_points: np.ndarray,
        desktop_points: np.ndarray,
        detection_index: int,
        high_res_image: np.ndarray = None
    ) -> Optional[ObjectAnalysisResult]:
        """
        基于点云分析对单个物体进行分类

        Args:
            detection: YOLO检测结果
            world_points: 物体的世界坐标系点云
            desktop_points: 物体的桌面坐标系点云
            detection_index: 检测结果的索引

        Returns:
            分析结果，如果点数不足则返回None
        """
        valid_desktop_points = desktop_points[~np.isnan(desktop_points).any(axis=1)]
        valid_world_points = world_points[~np.isnan(world_points).any(axis=1)]
        
        point_count = len(valid_desktop_points)

        if point_count < self.analysis_config.min_points_threshold:
            print(f"   - ⚠️ 警告: 目标 {detection.class_name} 的有效点云数 ({point_count}) 过少，跳过分析。")
            return None

        # Z值为高度
        heights = valid_desktop_points[:, 2]
        
        # 异常值过滤
        if self.analysis_config.enable_outlier_filtering:
            print(f"   🧹 开始异常值过滤...")
            print(f"     原始高度数组长度: {len(heights)}")
            print(f"     原始高度范围: {np.min(heights):.1f} ~ {np.max(heights):.1f}mm")
            
            filtered_heights, outlier_indices = self._filter_outliers_statistical(
                heights, 
                method=self.analysis_config.outlier_detection_method
            )
            
            print(f"     过滤后高度数组长度: {len(filtered_heights)}")
            print(f"     异常值索引数量: {len(outlier_indices)}")
            if len(outlier_indices) > 0:
                print(f"     异常值索引范围: {np.min(outlier_indices)} ~ {np.max(outlier_indices)}")
            
            # 如果过滤后点数不足，尝试其他方法
            if len(filtered_heights) < self.analysis_config.min_points_threshold:
                print(f"   ⚠️ {self.analysis_config.outlier_detection_method}方法过滤后点数不足，尝试修正Z-score方法...")
                filtered_heights, outlier_indices = self._filter_outliers_statistical(heights, method="modified_zscore")
                
                if len(filtered_heights) < self.analysis_config.min_points_threshold:
                    print(f"   ⚠️ 修正Z-score方法过滤后点数仍不足，使用原始数据...")
                    filtered_heights = heights
                    outlier_indices = np.array([])
        else:
            print(f"   ℹ️ 异常值过滤已禁用，使用原始数据...")
            filtered_heights = heights
            outlier_indices = np.array([])
        
        # 更新有效点云数据（移除异常值对应的点）
        if len(outlier_indices) > 0:
            print(f"   🔄 移除异常值对应的点云数据...")
            valid_indices = np.ones(len(valid_desktop_points), dtype=bool)
            valid_indices[outlier_indices] = False
            
            # 重新计算有效点云
            valid_desktop_points = valid_desktop_points[valid_indices]
            valid_world_points = valid_world_points[valid_indices]
            
            print(f"     过滤后有效点数: {len(valid_desktop_points)}")
        
        # 使用过滤后的高度进行统计
        heights = filtered_heights
        point_count = len(valid_desktop_points)

        # 计算基本统计信息
        average_height = np.mean(heights)
        height_range = np.max(heights) - np.min(heights) if len(heights) > 0 else 0.0

        # 计算基于物体自身平均高度的方差（传统方法）
        if len(heights) > 1:
            # 使用物体自身平均高度作为基准计算方差: Σ(height_i - average_height)² / N
            height_variance = np.var(heights)
        else:
            height_variance = 0.0

        # 打印调试统计信息（包含异常值过滤信息）
        self._debug_print_stats(heights, average_height, height_variance, average_height, height_range, point_count)
        
        # 额外统计信息：异常值过滤效果
        if len(outlier_indices) > 0:
            print(f"   📊 异常值过滤效果:")
            print(f"     原始点数: {len(valid_desktop_points) + len(outlier_indices)}")
            print(f"     过滤后点数: {len(valid_desktop_points)}")
            print(f"     过滤比例: {len(outlier_indices) / (len(valid_desktop_points) + len(outlier_indices)):.1%}")
            
            # 计算过滤前后的统计对比
            if len(outlier_indices) > 0:
                # 安全地重新计算原始统计信息用于对比
                try:
                    # 验证索引的有效性
                    valid_indices = outlier_indices[outlier_indices < len(heights)]
                    if len(valid_indices) > 0:
                        # 使用有效的索引重新构建原始数据
                        outlier_heights = heights[valid_indices]
                        all_heights = np.concatenate([heights, outlier_heights])
                        
                        original_variance = np.var(all_heights) if len(all_heights) > 1 else 0.0
                        original_range = np.max(all_heights) - np.min(all_heights) if len(all_heights) > 0 else 0.0
                        
                        print(f"     方差改善: {original_variance:.1f}mm² → {height_variance:.1f}mm² (改善: {((original_variance - height_variance) / original_variance * 100):.1f}%)")
                        print(f"     高度范围改善: {original_range:.1f}mm → {height_range:.1f}mm (改善: {((original_range - height_range) / original_range * 100):.1f}%)")
                    else:
                        print(f"     ⚠️ 无法计算原始统计信息：所有异常值索引都无效")
                except Exception as e:
                    print(f"     ⚠️ 计算原始统计信息失败: {e}")
                    print(f"     跳过统计对比计算")

        # 简化的桌面高度判断（仅用于过滤明显的平面物体）
        if self.coordinate_transformer.plane_transform_matrix is not None:
            # 桌面坐标系已建立，使用简单的高度阈值过滤
            table_height_threshold = -50.0  # 低于桌面20mm的物体认为是平面物体
            print(f"   📏 桌面坐标系已建立，使用高度阈值: {table_height_threshold:.1f}mm")
        else:
            # 如果未检测到桌面，使用更宽松的阈值
            table_height_threshold = -100.0  # 毫米
            print(f"   ⚠️ 未检测到桌面，使用宽松高度阈值: {table_height_threshold:.1f}mm")

        # 简化判断：高度明显低于桌面的物体直接设置为平面物体
        if average_height < table_height_threshold:
            print(f"   ❌ 物体平均高度 {average_height:.1f}mm 低于桌面阈值 {table_height_threshold:.1f}mm")
            print(f"   💡 此物体可能是桌面上的贴图、印刷品或嵌入桌面的物体，设置为平面物体")
            is_real = False
            reason = f"低于桌面(高度:{average_height:.1f}mm < {table_height_threshold:.1f}mm)"
        else:
            print(f"   ✅ 物体平均高度 {average_height:.1f}mm 高于桌面阈值 {table_height_threshold:.1f}mm，继续分析")

            # 获取分类阈值
            variance_threshold = self.analysis_config.variance_classification_threshold
            height_range_threshold = self.analysis_config.height_range_classification_threshold

            print(f"   🎯 分类阈值:")
            print(f"     方差阈值: {variance_threshold:.1f}mm²")
            print(f"     高度范围阈值: {height_range_threshold:.1f}mm")

            # 基于测试结果调整的分类逻辑：
            # 平面贴纸特征: 方差 < 8mm² 且 高度范围 < 15mm
            # 立体物体特征: 方差 ≥ 8mm² 或 高度范围 ≥ 15mm
            # 
            # 最新测试数据:
            # - orange: 方差=4.2mm², 高度范围=11.0mm → 平面物体 ✓
            # - scissors: 方差=6.8mm², 高度范围=12.1mm → 立体物体 ✓ (需要调整)
            # - hanger: 方差=9.5mm², 高度范围=20.9mm → 立体物体 ✓
            # - hanger: 方差=8.8mm², 高度范围=20.0mm → 立体物体 ✓ (需要调整)
            # - hanger: 方差=6.6mm², 高度范围=12.7mm → 立体物体 ✓ (需要调整)
            # - box: 方差=67.1mm², 高度范围=41.7mm → 立体物体 ✓
            # - can: 方差=29683.1mm², 高度范围=613.3mm → 立体物体 ✓
            # - bottle: 方差=5086.7mm², 高度范围=561.9mm → 立体物体 ✓
            # - can: 方差=22144.1mm², 高度范围=600.7mm → 立体物体 ✓
            
            # 调整后的阈值（基于最新测试结果）
            adjusted_variance_threshold = 8.0  # 从10.0调整到8.0
            adjusted_height_range_threshold = 15.0  # 从20.0调整到15.0
            
            print(f"   🔧 调整后的分类阈值:")
            print(f"     方差阈值: {adjusted_variance_threshold:.1f}mm² (原阈值: {variance_threshold:.1f}mm²)")
            print(f"     高度范围阈值: {adjusted_height_range_threshold:.1f}mm (原阈值: {height_range_threshold:.1f}mm)")
            
            # 改进的分类逻辑：同时考虑方差和高度范围
            # 1. 高方差 = 立体物体（物体内部有显著高度变化）
            # 2. 高高度范围 = 立体物体（物体有明显的高度差异）
            # 3. 低方差 + 低高度范围 = 平面物体
            
            is_high_variance = height_variance >= adjusted_variance_threshold
            is_high_height_range = height_range >= adjusted_height_range_threshold
            
            print(f"   📊 分类指标:")
            print(f"     方差: {height_variance:.1f}mm² {'≥' if is_high_variance else '<'} {adjusted_variance_threshold:.1f}mm²")
            print(f"     高度范围: {height_range:.1f}mm {'≥' if is_high_height_range else '<'} {adjusted_height_range_threshold:.1f}mm")
            
            if is_high_variance or is_high_height_range:
                # 满足任一条件即判定为立体物体
                is_real = True
                if is_high_variance and is_high_height_range:
                    reason = f"立体物体(方差:{height_variance:.1f}mm² ≥ {adjusted_variance_threshold:.1f}mm², 高度范围:{height_range:.1f}mm ≥ {adjusted_height_range_threshold:.1f}mm)"
                elif is_high_variance:
                    reason = f"立体物体(方差:{height_variance:.1f}mm² ≥ {adjusted_variance_threshold:.1f}mm²)"
                else:
                    reason = f"立体物体(高度范围:{height_range:.1f}mm ≥ {adjusted_height_range_threshold:.1f}mm)"
                print(f"   ✅ 物体内部有显著高度变化，判定为立体物体")
            else:
                # 两个条件都不满足，判定为平面物体
                is_real = False
                reason = f"平面物体(方差:{height_variance:.1f}mm² < {adjusted_variance_threshold:.1f}mm², 高度范围:{height_range:.1f}mm < {adjusted_height_range_threshold:.1f}mm)"
                print(f"   ❌ 物体内部高度变化很小，判定为平面物体")

        print(f"   分类结果: {'真实物体' if is_real else '平面物体'} - {reason}")
        
        confidence = 1.0 # 简化置信度逻辑

        # 创建结果对象
        result = ObjectAnalysisResult(detection=detection)
        
        # 设置检测索引
        result.detection_index = detection_index
        
        # 设置基本属性（使用毫米单位）
        result.point_count = point_count
        result.height_variance = height_variance  # 使用毫米²单位
        result.average_height = average_height  # 平均高度（毫米）
        # 保存高度范围（毫米）
        result.height_range = (np.min(heights) if len(heights) > 0 else 0.0,
                              np.max(heights) if len(heights) > 0 else 0.0)
        result.is_real_object = is_real
        result.confidence = confidence
        result.classification_confidence = confidence
        result.classification = "真实物体" if is_real else "平面物体"
        
        # 设置点云数据
        result.camera_points = valid_world_points
        result.desktop_points = valid_desktop_points

        # 兼容旧版本的属性
        result.points_3d_camera = valid_world_points
        result.points_3d_desktop = valid_desktop_points

        # 如果是book类的真实物体，进行文字识别
        if (is_real and detection.class_name.lower() == 'book' and
            self.text_recognizer and self.affine_mapper and high_res_image is not None):
            print(f"   📝 检测到真实book物体，开始文字识别...")
            try:
                book_subcategory = self._perform_text_recognition_for_book(detection, high_res_image)
                if book_subcategory:
                    # 将分类结果保存到detection对象中
                    detection.book_subcategory = book_subcategory
                    print(f"   ✅ Book分类完成: {book_subcategory}")
                else:
                    print(f"   ⚠️ Book文字识别未能确定分类")
            except Exception as e:
                print(f"   ❌ Book文字识别失败: {e}")

        return result

    def _perform_text_recognition_for_book(self, detection, high_res_image: np.ndarray) -> Optional[str]:
        """
        对book类物体进行文字识别和分类

        Args:
            detection: 检测结果对象
            high_res_image: 高分辨率图像

        Returns:
            book的子分类结果，如果识别失败则返回None
        """
        try:
            # 1. 验证高分辨率图像
            if high_res_image is None:
                print(f"   ❌ 高分辨率图像为空")
                return None

            print(f"   ✅ 使用传入的高分辨率图像: {high_res_image.shape}")

            # 2. 映射检测结果到高分辨率
            image_shape_640 = (480, 640)  # (height, width)
            mapped_region = self.affine_mapper.map_detection_to_high_res(detection, image_shape_640)

            if not mapped_region:
                print(f"   ❌ 检测结果映射失败")
                return None

            # 3. 从高分辨率图像中提取ROI
            roi, adjusted_bbox = self.affine_mapper.create_roi_from_mapped_region(
                high_res_image, mapped_region, padding=10
            )

            if roi is None:
                print(f"   ❌ ROI提取失败")
                return None

            print(f"   ✅ 成功提取ROI: {roi.shape}")

            # 4. 进行文字识别
            text_results = self.text_recognizer.recognize_text_in_roi(roi, mapped_region)

            if not text_results:
                print(f"   ⚠️ 未识别到文字")
                return None

            # 5. 基于识别的文字进行book分类
            all_text = " ".join([result.text for result in text_results])
            print(f"   📝 识别到的文字: '{all_text}'")

            # 使用现有的book分类逻辑
            book_subcategory = self._classify_book_by_text(all_text)

            return book_subcategory

        except Exception as e:
            print(f"   ❌ Book文字识别过程失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _classify_book_by_text(self, text: str) -> str:
        """
        基于识别的文字对book进行分类

        Args:
            text: 识别到的文字内容

        Returns:
            book的子分类
        """
        if not text or not text.strip():
            return "未分类书籍"

        text_lower = text.lower()

        # 定义分类关键词
        categories = {
            "教科书": ["教材", "教科书", "课本", "textbook", "教程", "tutorial", "学习", "study"],
            "小说": ["小说", "novel", "故事", "story", "fiction", "文学", "literature"],
            "技术书": ["编程", "programming", "技术", "technology", "计算机", "computer", "软件", "software", "算法", "algorithm"],
            "工具书": ["词典", "dictionary", "手册", "manual", "指南", "guide", "参考", "reference"],
            "杂志": ["杂志", "magazine", "期刊", "journal", "月刊", "weekly", "daily"]
        }

        # 计算每个分类的匹配分数
        best_category = "未分类书籍"
        best_score = 0.0

        for category, keywords in categories.items():
            score = self._calculate_text_similarity(text_lower, keywords)
            if score > best_score and score > 0.3:  # 设置最低阈值
                best_score = score
                best_category = category

        print(f"   📚 Book分类结果: {best_category} (置信度: {best_score:.3f})")
        return best_category


def test_object_analyzer():
    """测试物体分析器"""
    print("🧪 测试物体分析器...")
    
    try:
        # 这里需要实际的相机和检测器才能测试
        print("⚠️ 物体分析器测试需要相机和YOLO检测器")
        print("   请运行主程序进行完整测试")
        
        # 创建模拟配置
        from config import SystemConfig
        config = SystemConfig()
        
        # 打印配置信息
        print(f"📋 分析配置:")
        print(f"   新的分类阈值 (mm): 方差 > 500 mm² 或 高度差 > 20 mm")
        print(f"   最小点数: {config.object_analysis.min_points_threshold}")
        print(f"   高度范围 (参考): {config.object_analysis.min_height_threshold*1000}~{config.object_analysis.max_height_threshold*1000}mm")
        
        # 添加调整后的阈值信息
        print(f"\n🔧 调整后的分类阈值（基于最新测试结果）:")
        print(f"   方差阈值: 8.0mm² (原阈值: {config.object_analysis.variance_classification_threshold:.1f}mm²)")
        print(f"   高度范围阈值: 15.0mm (原阈值: {config.object_analysis.height_range_classification_threshold:.1f}mm)")
        print(f"\n📊 最新测试数据验证:")
        print(f"   平面贴纸:")
        print(f"     - orange: 方差=4.2mm², 高度范围=11.0mm → 平面物体 ✓")
        print(f"   立体物体:")
        print(f"     - scissors: 方差=6.8mm², 高度范围=12.1mm → 立体物体 ✓")
        print(f"     - hanger: 方差=9.5mm², 高度范围=20.9mm → 立体物体 ✓")
        print(f"     - hanger: 方差=8.8mm², 高度范围=20.0mm → 立体物体 ✓")
        print(f"     - hanger: 方差=6.6mm², 高度范围=12.7mm → 立体物体 ✓")
        print(f"     - box: 方差=67.1mm², 高度范围=41.7mm → 立体物体 ✓")
        print(f"     - can: 方差=29683.1mm², 高度范围=613.3mm → 立体物体 ✓")
        print(f"     - bottle: 方差=5086.7mm², 高度范围=561.9mm → 立体物体 ✓")
        print(f"     - can: 方差=22144.1mm², 高度范围=600.7mm → 立体物体 ✓")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_object_analyzer() 