#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO物体检测模块
基于YOLOv8实现图像中的物体检测，返回边界框信息
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
import time
from datetime import datetime
import os
from dataclasses import dataclass

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("⚠️ 警告: ultralytics未安装，YOLO功能将不可用")
    print("   安装命令: pip install ultralytics")

from config import SystemConfig, get_class_name, PathConfig


@dataclass
class Detection:
    """
    存储单个检测结果的信息
    - box: (x1, y1, x2, y2)
    - mask: 可选的二值掩码
    """
    box: np.ndarray
    confidence: float
    class_id: int
    class_name: str
    mask: Optional[np.ndarray] = None
    book_subcategory: Optional[str] = None  # book的具体分类（数学类书籍、语文类书籍等）

    def __post_init__(self):
        """为了向后兼容旧代码，添加与原class相同的属性"""
        self.bbox = tuple(map(int, self.box))
        self.x1, self.y1, self.x2, self.y2 = self.bbox
        self.width = self.x2 - self.x1
        self.height = self.y2 - self.y1
        self.area = self.width * self.height
        self.center_x = (self.x1 + self.x2) // 2
        self.center_y = (self.y1 + self.y2) // 2

    def __str__(self):
        if self.class_name.lower() == 'book' and self.book_subcategory:
            return f"Detection({self.class_name}-{self.book_subcategory}, conf={self.confidence:.3f}, bbox={self.bbox})"
        return f"Detection({self.class_name}, conf={self.confidence:.3f}, bbox={self.bbox})"

    def __repr__(self):
        return self.__str__()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            'bbox': self.bbox,
            'confidence': float(self.confidence),
            'class_id': int(self.class_id),
            'class_name': self.class_name,
            'width': self.width,
            'height': self.height,
            'area': self.area,
            'center': (self.center_x, self.center_y)
        }
        if self.book_subcategory:
            result['book_subcategory'] = self.book_subcategory
        return result


class YOLODetector:
    """
    YOLO物体检测器
    基于YOLOv8实现图像中的物体检测
    """
    
    def __init__(self, config: SystemConfig = None):
        """
        初始化YOLO检测器
        
        Args:
            config: 系统配置对象
        """
        if not YOLO_AVAILABLE:
            raise ImportError("ultralytics未安装，无法使用YOLO功能")
        
        if config is None:
            from config import default_config
            config = default_config
        
        self.config = config
        self.yolo_config = config.yolo
        
        self.max_det = 100  # 默认最大检测数量
        self.device = self._get_device(self.yolo_config.device)
        self.target_classes = self.yolo_config.target_classes
        self.model = None

        # YOLO输入图像尺寸设置
        self.input_size = (1080, 1920)  # (height, width) 格式

        # 修正：直接使用配置中的模型路径，如果路径是相对路径则拼接
        if os.path.isabs(self.yolo_config.model_path):
            self.model_path = self.yolo_config.model_path
        else:
            # 如果是相对路径，则相对于项目根目录
            self.model_path = os.path.join(PathConfig.PROJECT_DIR, self.yolo_config.model_path)
        
        self.load_model()
    
    def load_model(self):
        """加载YOLO模型"""
        try:
            print(f"📥 加载YOLO模型: {self.model_path}")
            # 检查模型文件是否存在
            if not os.path.exists(self.model_path):
                print(f"   模型文件不存在，将尝试从网络下载到: {self.model_dir}")
                # Ultralytics库会在加载时自动处理下载，并保存到指定的路径
            
            # 设置YOLO的模型目录为我们统一管理的目录
            os.environ['YOLO_VERBOSE'] = 'False' # 减少不必要的日志
            
            self.model = YOLO(self.model_path)
            
            # 将模型移动到我们指定的设备
            self.model.to(self.device)
            
            print("✅ YOLO模型加载成功")
            #print(f"   模型类型: {self.model.type}")
            #print(f"   支持的类别数: {len(self.model.names)}")
            #print(f"   计算设备: {self.device}")
            
        except Exception as e:
            print(f"❌ YOLO模型加载失败: {e}")
            print(f"💡 请检查模型路径: {self.model_path}")
            print(f"💡 或者检查网络连接是否可以下载模型。")
            self.model = None
    
    def _get_device(self, device_str: str) -> str:
        """获取计算设备"""
        if device_str == "cpu":
            return "cpu"
        elif device_str == "cuda":
            return "cuda"
        else:
            raise ValueError("不支持的设备类型")
    
    def detect_objects(self, image: np.ndarray) -> List[Detection]:
        """
        使用YOLOv8对图像进行物体检测与分割
        Args:
            image: 输入的OpenCV图像 (BGR)
        Returns:
            一个包含Detection对象的列表
        """
        if not self.model:
            print(f"❌ YOLO模型未加载")
            return []
        
        if image is None or image.size == 0:
            print(f"❌ 输入图像无效")
            return []

        detections = []
        try:
            start_time = time.time()
            results = self.model.predict(
                source=image,
                conf=self.yolo_config.confidence_threshold,
                iou=self.yolo_config.iou_threshold,
                max_det=self.max_det,
                device=self.device,
                imgsz=self.input_size,  # 设置输入图像尺寸为 (480 , 640)
                verbose=False
            )
            detection_time = time.time() - start_time
            
            result = results[0]
            
            has_masks = result.masks is not None and len(result.masks) > 0

            for i in range(len(result.boxes)):
                box = result.boxes[i]
                xyxy = box.xyxy[0].cpu().numpy()
                conf = float(box.conf[0].cpu().numpy())
                cls_id = int(box.cls[0].cpu().numpy())
                class_name = self.model.names[cls_id]

                obj_mask = None
                if has_masks and i < len(result.masks):
                    mask_data = result.masks[i].data[0].cpu().numpy()
                    obj_mask = (mask_data > 0).astype(np.uint8)

                detection = Detection(
                    box=xyxy,
                    confidence=conf,
                    class_id=cls_id,
                    class_name=class_name,
                    mask=obj_mask
                )
                detections.append(detection)
            
            print(f"🎯 检测完成: {len(detections)} 个物体 (用时: {detection_time:.3f}s)")
            
            if detections:
                class_counts = {}
                for det in detections:
                    class_counts[det.class_name] = class_counts.get(det.class_name, 0) + 1
                
                print(f"   检测到的物体类别:")
                for class_name, count in class_counts.items():
                    print(f"     {class_name}: {count} 个")

        except Exception as e:
            print(f"❌ YOLO检测过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return []
            
        return detections
    
    def detect_objects_from_frame(self, frame) -> List[Detection]:
        """
        从相机帧中检测物体
        
        Args:
            frame: 相机帧对象
            
        Returns:
            检测结果列表
        """
        try:
            # 将帧转换为OpenCV图像
            width = frame.get_width()
            height = frame.get_height()
            
            # 获取图像数据
            frame_data = np.frombuffer(frame.get_data(), dtype=np.uint8)
            
            # 判断是彩色还是深度帧
            if len(frame_data) == width * height * 3:
                # 彩色帧 (RGB)
                image = frame_data.reshape((height, width, 3))
                # RGB转BGR (OpenCV格式)
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            elif len(frame_data) == width * height * 2:
                # 深度帧，转换为可视化图像
                depth_data = frame_data.view(np.uint16).reshape((height, width))
                
                # 获取深度范围并归一化
                if hasattr(frame, 'get_depth_scale'):
                    scale = frame.get_depth_scale()
                    depth_meters = depth_data.astype(np.float32) * scale
                else:
                    depth_meters = depth_data.astype(np.float32)
                
                # 归一化到0-255范围
                depth_normalized = cv2.normalize(depth_meters, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
                
                # 应用彩色映射
                image = cv2.applyColorMap(depth_normalized, cv2.COLORMAP_JET)
            else:
                print(f"❌ 不支持的帧格式: 数据长度 {len(frame_data)}, 期望 {width*height*3} 或 {width*height*2}")
                return []
            
            return self.detect_objects(image)
            
        except Exception as e:
            print(f"❌ 从帧检测物体失败: {e}")
            return []
    
    def draw_detections(self, image: np.ndarray, detections: List[Detection], 
                       draw_confidence: bool = True, draw_class: bool = True) -> np.ndarray:
        """
        在图像上绘制检测结果
        
        Args:
            image: 输入图像
            detections: 检测结果列表
            draw_confidence: 是否绘制置信度
            draw_class: 是否绘制类别名称
            
        Returns:
            绘制了检测框的图像
        """
        if image is None:
            return None
        
        # 复制图像以避免修改原图
        result_image = image.copy()
        
        # 颜色列表（BGR格式）
        colors = [
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 0, 255),    # 红色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 品红
            (0, 255, 255),  # 黄色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
            (128, 128, 128),# 灰色
            (255, 192, 203) # 粉色
        ]
        
        for i, detection in enumerate(detections):
            # 选择颜色
            color = colors[i % len(colors)]
            
            # 绘制边界框
            cv2.rectangle(result_image, 
                         (detection.x1, detection.y1), 
                         (detection.x2, detection.y2), 
                         color, 2)
            
            # 准备标签文本
            label_parts = []
            if draw_class:
                label_parts.append(detection.class_name)
            if draw_confidence:
                label_parts.append(f"{detection.confidence:.3f}")
            
            if label_parts:
                label = " ".join(label_parts)
                
                # 计算文本大小
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.25
                thickness = 1
                (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
                
                # 绘制文本背景
                cv2.rectangle(result_image,
                             (detection.x1, detection.y1 - text_height - baseline - 5),
                             (detection.x1 + text_width, detection.y1),
                             color, -1)
                
                # 绘制文本
                cv2.putText(result_image, label,
                           (detection.x1, detection.y1 - baseline - 2),
                           font, font_scale*0.8, (255, 255, 255), thickness)
            
            # 绘制中心点
            cv2.circle(result_image, (detection.center_x, detection.center_y), 3, color, -1)
        
        return result_image
    
    def save_detection_result(self, image: np.ndarray, detections: List[Detection], 
                            save_path: str = None) -> str:
        """
        保存检测结果图像
        
        Args:
            image: 原始图像
            detections: 检测结果
            save_path: 保存路径
            
        Returns:
            保存的文件路径
        """
        try:
            # 绘制检测结果
            result_image = self.draw_detections(image, detections)
            
            if save_path is None:
                # 自动生成保存路径
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = self.config.paths.get_output_path(f"yolo_detection_{timestamp}.png")
            
            # 保存图像
            cv2.imwrite(save_path, result_image)
            print(f"💾 检测结果已保存: {save_path}")
            
            return save_path
            
        except Exception as e:
            print(f"❌ 保存检测结果失败: {e}")
            return None
    
    def get_bbox_pixels(self, image_shape: Tuple[int, int], bbox: Tuple[int, int, int, int]) -> List[Tuple[int, int]]:
        """
        获取边界框内的所有像素坐标
        
        Args:
            image_shape: 图像形状 (height, width)
            bbox: 边界框 (x1, y1, x2, y2)
            
        Returns:
            像素坐标列表 [(u, v), ...]
        """
        height, width = image_shape
        x1, y1, x2, y2 = bbox
        
        # 确保边界框在图像范围内
        x1 = max(0, min(x1, width - 1))
        y1 = max(0, min(y1, height - 1))
        x2 = max(0, min(x2, width - 1))
        y2 = max(0, min(y2, height - 1))
        
        # 生成边界框内的所有像素坐标
        pixels = []
        for v in range(y1, y2 + 1):
            for u in range(x1, x2 + 1):
                pixels.append((u, v))
        
        return pixels
    
    def get_bbox_center_pixels(self, bbox: Tuple[int, int, int, int], 
                             grid_size: int = 5) -> List[Tuple[int, int]]:
        """
        获取边界框中心区域的采样像素坐标
        
        Args:
            bbox: 边界框 (x1, y1, x2, y2)
            grid_size: 网格大小 (grid_size x grid_size)
            
        Returns:
            采样像素坐标列表
        """
        x1, y1, x2, y2 = bbox
        
        # 计算网格间距
        width = x2 - x1
        height = y2 - y1
        
        step_x = max(1, width // grid_size)
        step_y = max(1, height // grid_size)
        
        pixels = []
        for i in range(grid_size):
            for j in range(grid_size):
                u = x1 + int((i + 0.5) * step_x)
                v = y1 + int((j + 0.5) * step_y)
                
                # 确保坐标在边界框内
                u = max(x1, min(u, x2))
                v = max(y1, min(v, y2))
                
                pixels.append((u, v))
        
        return pixels
    
    def filter_detections_by_area(self, detections: List[Detection], 
                                min_area: int = 100, max_area: int = None) -> List[Detection]:
        """
        根据面积过滤检测结果
        
        Args:
            detections: 检测结果列表
            min_area: 最小面积
            max_area: 最大面积
            
        Returns:
            过滤后的检测结果
        """
        filtered = []
        for detection in detections:
            if detection.area >= min_area:
                if max_area is None or detection.area <= max_area:
                    filtered.append(detection)
        
        if len(filtered) != len(detections):
            print(f"🔍 面积过滤: {len(detections)} -> {len(filtered)} 个检测结果")
        
        return filtered
    
    def get_detection_stats(self, detections: List[Detection]) -> Dict[str, Any]:
        """
        获取检测结果统计信息
        
        Args:
            detections: 检测结果列表
            
        Returns:
            统计信息字典
        """
        if not detections:
            return {
                'total_count': 0,
                'class_counts': {},
                'confidence_stats': {},
                'area_stats': {}
            }
        
        # 类别统计
        class_counts = {}
        for det in detections:
            class_counts[det.class_name] = class_counts.get(det.class_name, 0) + 1
        
        # 置信度统计
        confidences = [det.confidence for det in detections]
        confidence_stats = {
            'mean': np.mean(confidences),
            'min': np.min(confidences),
            'max': np.max(confidences),
            'std': np.std(confidences)
        }
        
        # 面积统计
        areas = [det.area for det in detections]
        area_stats = {
            'mean': np.mean(areas),
            'min': np.min(areas),
            'max': np.max(areas),
            'std': np.std(areas)
        }
        
        return {
            'total_count': len(detections),
            'class_counts': class_counts,
            'confidence_stats': confidence_stats,
            'area_stats': area_stats
        }


def test_yolo_detector():
    """测试YOLO检测器"""
    print("🧪 测试YOLO检测器...")
    
    try:
        # 创建检测器
        detector = YOLODetector()
        
        # 创建测试图像
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        test_image[100:200, 100:200] = [255, 0, 0]  # 红色方块
        test_image[300:400, 400:500] = [0, 255, 0]  # 绿色方块
        
        print("🔍 运行检测测试...")
        detections = detector.detect_objects(test_image)
        
        print(f"✅ 检测完成: {len(detections)} 个物体")
        
        if detections:
            for i, det in enumerate(detections):
                print(f"   {i+1}. {det}")
        
        # 测试绘制功能
        result_image = detector.draw_detections(test_image, detections)
        if result_image is not None:
            print("✅ 绘制检测结果成功")
        
        # 测试保存功能
        save_path = detector.save_detection_result(test_image, detections)
        if save_path:
            print(f"✅ 保存测试结果: {save_path}")
        
        # 测试统计功能
        stats = detector.get_detection_stats(detections)
        print(f"📊 检测统计: {stats}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_yolo_detector() 