#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文字识别集成到物体分析中的功能
"""

import numpy as np
from typing import List, Tuple
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_object_analyzer_with_text_recognition():
    """测试ObjectAnalyzer与文字识别的集成"""
    print("🧪 测试ObjectAnalyzer与文字识别的集成...")
    
    try:
        # 导入必要的模块
        from object_analyzer import ObjectAnalyzer
        from coordinate_transformer import CoordinateTransformer
        from config import default_config
        from yolo_detector import Detection
        from affine_mapper import AffineMapper
        from text_recognizer import TextRecognizer
        from camera_controller import CameraController
        
        print("✅ 模块导入成功")
        
        # 创建模拟的组件
        coord_transformer = CoordinateTransformer(default_config)
        
        # 模拟文字识别组件
        try:
            calibration_path = default_config.text_recognition.get_calibration_file_path()
            affine_mapper = AffineMapper(calibration_path)
            print("✅ 仿射变换映射器创建成功")
        except Exception as e:
            print(f"⚠️ 仿射变换映射器创建失败: {e}")
            affine_mapper = None
        
        try:
            text_recognizer = TextRecognizer(
                rec_model_name=default_config.text_recognition.recognition_model,
                det_model_name=default_config.text_recognition.detection_model
            )
            print("✅ 文字识别器创建成功")
        except Exception as e:
            print(f"⚠️ 文字识别器创建失败: {e}")
            text_recognizer = None
        
        try:
            camera_controller = CameraController(default_config.camera)
            print("✅ 相机控制器创建成功")
        except Exception as e:
            print(f"⚠️ 相机控制器创建失败: {e}")
            camera_controller = None
        
        # 创建ObjectAnalyzer实例
        analyzer = ObjectAnalyzer(
            coord_transformer,
            default_config,
            text_recognizer=text_recognizer,
            affine_mapper=affine_mapper,
            camera_controller=camera_controller
        )
        print("✅ ObjectAnalyzer创建成功")
        
        # 检查analyze_objects_batch方法的签名
        import inspect
        sig = inspect.signature(analyzer.analyze_objects_batch)
        params = list(sig.parameters.keys())
        print(f"📋 analyze_objects_batch方法参数: {params}")
        
        if 'high_res_image' in params:
            print("✅ analyze_objects_batch方法已支持high_res_image参数")
        else:
            print("❌ analyze_objects_batch方法缺少high_res_image参数")
        
        # 检查_classify_object方法的签名
        sig2 = inspect.signature(analyzer._classify_object)
        params2 = list(sig2.parameters.keys())
        print(f"📋 _classify_object方法参数: {params2}")
        
        if 'high_res_image' in params2:
            print("✅ _classify_object方法已支持high_res_image参数")
        else:
            print("❌ _classify_object方法缺少high_res_image参数")
        
        # 检查_perform_text_recognition_for_book方法的签名
        sig3 = inspect.signature(analyzer._perform_text_recognition_for_book)
        params3 = list(sig3.parameters.keys())
        print(f"📋 _perform_text_recognition_for_book方法参数: {params3}")
        
        if 'high_res_image' in params3:
            print("✅ _perform_text_recognition_for_book方法已支持high_res_image参数")
        else:
            print("❌ _perform_text_recognition_for_book方法缺少high_res_image参数")
        
        print("\n🎉 文字识别集成测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_affine_mapper_functions():
    """测试仿射变换映射器的关键函数"""
    print("\n🧪 测试仿射变换映射器的关键函数...")
    
    try:
        from affine_mapper import AffineMapper
        from config import default_config
        
        # 创建AffineMapper实例
        calibration_path = default_config.text_recognition.get_calibration_file_path()
        mapper = AffineMapper(calibration_path)
        
        # 检查关键函数是否存在
        functions_to_check = [
            'transform_point_640_to_1920',
            'transform_bbox_640_to_1920',
            'map_point_1920_to_640',
            'map_bbox_1920_to_640',
            'is_calibration_loaded'
        ]
        
        for func_name in functions_to_check:
            if hasattr(mapper, func_name):
                print(f"✅ {func_name} 函数存在")
            else:
                print(f"❌ {func_name} 函数不存在")
        
        # 测试transform_point_640_to_1920函数
        if hasattr(mapper, 'transform_point_640_to_1920') and mapper.is_calibration_loaded():
            try:
                # 测试点变换
                x_640, y_640 = 320, 240  # 640×480的中心点
                x_1920, y_1920 = mapper.transform_point_640_to_1920(x_640, y_640)
                print(f"✅ 点变换测试: ({x_640}, {y_640}) → ({x_1920:.1f}, {y_1920:.1f})")
            except Exception as e:
                print(f"❌ 点变换测试失败: {e}")
        
        print("✅ 仿射变换映射器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 仿射变换映射器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始文字识别集成测试...")
    
    # 测试1: ObjectAnalyzer与文字识别的集成
    test1_result = test_object_analyzer_with_text_recognition()
    
    # 测试2: 仿射变换映射器的关键函数
    test2_result = test_affine_mapper_functions()
    
    # 总结
    print(f"\n📊 测试结果总结:")
    print(f"   ObjectAnalyzer集成测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   仿射变换映射器测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！文字识别已成功集成到物体分析中。")
        print("💡 现在可以运行main.py，文字识别将在确认book类为真实物体后自动进行。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关代码。")

if __name__ == '__main__':
    main()
