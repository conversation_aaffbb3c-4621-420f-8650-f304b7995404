#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
物体输出生成器
用于生成指定格式的物体检测结果txt文件
"""

import os
from typing import List, Dict
from datetime import datetime
from config import PathConfig


class ObjectOutputGenerator:
    """物体输出生成器"""
    
    def __init__(self):
        """初始化输出生成器"""
        # 物体类别名称到Goal_ID的映射
        self.class_to_goal_id = {
            # 原有的8类物体
            'hanger': 'CA001',
            'toothbrush': 'CA002',
            'jelly': 'CB001',
            'box': 'CB002',
            'can': 'CC001',
            'bottle': 'CC002',
            'banana': 'CD001',
            'orange': 'CD002',
            # 新增book分类
            '数学类书籍': 'w001',
            '语文类书籍': 'w002',
        }
        
        # 支持的类别名称别名（只包含需要输出到txt文件的8类）
        self.class_aliases = {
            'toothbrush': ['toothbrush'],
            'jelly': ['jelly', 'gelatin'],
            'box': ['box', 'package', 'container'],
            'can': ['can', 'tin'],
            'bottle': ['bottle'],
            'banana': ['banana'],
            'orange': ['orange'],
            'hanger': ['hanger', 'coat_hanger']
        }
        
        # 创建反向映射（从别名到标准名称）
        self.alias_to_standard = {}
        for standard_name, aliases in self.class_aliases.items():
            for alias in aliases:
                self.alias_to_standard[alias.lower()] = standard_name
    
    def _normalize_class_name(self, class_name: str) -> str:
        """
        标准化类别名称
        
        Args:
            class_name: 原始类别名称
            
        Returns:
            标准化的类别名称，如果无法识别则返回原名称
        """
        normalized = class_name.lower().strip()
        return self.alias_to_standard.get(normalized, normalized)
    
    def _count_real_objects(self, analysis_results: List) -> Dict[str, int]:
        """
        统计真实物体的数量
        
        Args:
            analysis_results: 物体分析结果列表
            
        Returns:
            物体类别名称到数量的映射
        """
        object_counts = {}
        
        for result in analysis_results:
            # 只统计真实物体（非平面物体）
            if hasattr(result, 'is_real_object') and result.is_real_object:
                class_name = getattr(result, 'class_name', 'unknown')
                normalized_name = self._normalize_class_name(class_name)
                
                # 只统计我们关心的物体类别
                if normalized_name in self.class_to_goal_id:
                    object_counts[normalized_name] = object_counts.get(normalized_name, 0) + 1
                else:
                    print(f"⚠️ 检测到未知物体类别: {class_name} (标准化: {normalized_name})")
        
        # 统计book分类数量
        for result in analysis_results:
            if (hasattr(result, 'is_real_object') and result.is_real_object and 
                hasattr(result, 'detection') and result.detection and 
                result.detection.class_name.lower() == 'book'):
                
                if hasattr(result.detection, 'book_subcategory') and result.detection.book_subcategory:
                    subcategory = result.detection.book_subcategory
                    if subcategory in self.class_to_goal_id:
                        object_counts[subcategory] = object_counts.get(subcategory, 0) + 1
        
        return object_counts
    
    def generate_output_content(self, analysis_results: List) -> str:
        """
        生成输出文件内容
        
        Args:
            analysis_results: 物体分析结果列表
            
        Returns:
            格式化的输出文件内容
        """
        # 统计真实物体数量（包括book分类）
        object_counts = self._count_real_objects(analysis_results)
        
        # 生成输出内容
        lines = ["START"]
        
        # 按Goal_ID排序输出（确保CA、CB、CC、CD、w001、w002的顺序）
        goal_id_items = []
        for class_name in object_counts.keys():
            goal_id = self.class_to_goal_id[class_name]
            count = object_counts[class_name]
            goal_id_items.append((goal_id, count))
        
        # 按Goal_ID排序
        goal_id_items.sort(key=lambda x: x[0])
        
        for goal_id, count in goal_id_items:
            lines.append(f"Goal_ID={goal_id};Num={count}")
        
        lines.append("END")
        
        return "\n".join(lines)
    
    def save_output_file(self, analysis_results: List, frame_name: str = None) -> str:
        """
        保存输出文件
        
        Args:
            analysis_results: 物体分析结果列表
            frame_name: 帧名称，如果为None则使用时间戳
            
        Returns:
            保存的文件路径
        """
        # 生成文件内容
        content = self.generate_output_content(analysis_results)
        
        # 生成文件名
        if frame_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"result_{timestamp}.txt"
        else:
            filename = f"result_{frame_name}.txt"
        
        # 确保输出目录存在
        os.makedirs(PathConfig.OUTPUT_DIR, exist_ok=True)
        
        # 保存文件
        filepath = os.path.join(PathConfig.OUTPUT_DIR, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 物体检测结果已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ 保存输出文件失败: {e}")
            return None
    
    def print_detection_summary(self, analysis_results: List):
        """
        打印检测结果摘要
        
        Args:
            analysis_results: 物体分析结果列表
        """
        object_counts = self._count_real_objects(analysis_results)
        
        print("\n" + "="*40)
        print("📋 物体检测结果摘要")
        print("="*40)
        
        if not object_counts:
            print("⚠️ 未检测到任何已知的真实物体")
            return
        
        total_objects = sum(object_counts.values())
        print(f"📊 检测到 {len(object_counts)} 种物体，共 {total_objects} 个")
        print()
        
        # 按Goal_ID排序显示
        goal_id_items = []
        for class_name in object_counts.keys():
            goal_id = self.class_to_goal_id[class_name]
            count = object_counts[class_name]
            goal_id_items.append((goal_id, class_name, count))
        
        # 按Goal_ID排序
        goal_id_items.sort(key=lambda x: x[0])
        
        for goal_id, class_name, count in goal_id_items:
            print(f"   {goal_id}: {class_name} × {count}")
        
        print("="*40)
    
    def get_supported_classes(self) -> List[str]:
        """
        获取支持的物体类别列表
        
        Returns:
            支持的物体类别名称列表
        """
        return list(self.class_to_goal_id.keys())
    
    def get_class_mapping(self) -> Dict[str, str]:
        """
        获取类别名称到Goal_ID的映射
        
        Returns:
            类别名称到Goal_ID的映射字典
        """
        return self.class_to_goal_id.copy() 