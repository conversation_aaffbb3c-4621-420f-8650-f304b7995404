#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文字识别修复
"""

import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_text_recognition_method_signature():
    """测试文字识别方法的签名"""
    print("🧪 测试文字识别方法的签名...")
    
    try:
        from text_recognizer import TextRecognizer
        from affine_mapper import MappedRegion
        import inspect
        
        # 创建一个模拟的TextRecognizer实例
        try:
            recognizer = TextRecognizer()
            print("✅ TextRecognizer创建成功")
        except Exception as e:
            print(f"⚠️ TextRecognizer创建失败: {e}")
            print("   这是正常的，因为需要模型文件")
            # 我们仍然可以检查方法签名
            recognizer = None
        
        # 检查recognize_text_in_roi方法的签名
        sig = inspect.signature(TextRecognizer.recognize_text_in_roi)
        params = list(sig.parameters.keys())
        print(f"📋 recognize_text_in_roi方法参数: {params}")
        
        # 检查参数类型
        for param_name, param in sig.parameters.items():
            if param_name == 'region_info':
                print(f"   参数 {param_name}: {param.annotation}")
                if 'MappedRegion' in str(param.annotation):
                    print("   ✅ region_info参数类型正确 (MappedRegion)")
                else:
                    print("   ⚠️ region_info参数类型可能不正确")
        
        # 测试MappedRegion的创建
        try:
            test_region = MappedRegion(
                bbox=(100, 100, 200, 200),
                confidence=0.8,
                class_name="book"
            )
            print("✅ MappedRegion创建成功")
            print(f"   bbox: {test_region.bbox}")
            print(f"   class_name: {test_region.class_name}")
            print(f"   confidence: {test_region.confidence}")
        except Exception as e:
            print(f"❌ MappedRegion创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_object_analyzer_text_recognition_call():
    """测试ObjectAnalyzer中文字识别调用的修复"""
    print("\n🧪 测试ObjectAnalyzer中文字识别调用的修复...")
    
    try:
        from object_analyzer import ObjectAnalyzer
        import inspect
        
        # 检查_perform_text_recognition_for_book方法
        sig = inspect.signature(ObjectAnalyzer._perform_text_recognition_for_book)
        params = list(sig.parameters.keys())
        print(f"📋 _perform_text_recognition_for_book方法参数: {params}")
        
        if 'high_res_image' in params:
            print("✅ _perform_text_recognition_for_book方法已支持high_res_image参数")
        else:
            print("❌ _perform_text_recognition_for_book方法缺少high_res_image参数")
        
        # 检查方法源码中是否正确调用了recognize_text_in_roi
        import inspect
        source = inspect.getsource(ObjectAnalyzer._perform_text_recognition_for_book)
        
        if 'recognize_text_in_roi(roi, mapped_region)' in source:
            print("✅ recognize_text_in_roi调用已修复，使用mapped_region参数")
        elif 'recognize_text_in_roi(roi, f"book_' in source:
            print("❌ recognize_text_in_roi调用仍使用字符串参数，需要修复")
        else:
            print("⚠️ 无法确定recognize_text_in_roi的调用方式")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始文字识别修复测试...")
    
    # 测试1: 文字识别方法签名
    test1_result = test_text_recognition_method_signature()
    
    # 测试2: ObjectAnalyzer中文字识别调用的修复
    test2_result = test_object_analyzer_text_recognition_call()
    
    # 总结
    print(f"\n📊 测试结果总结:")
    print(f"   文字识别方法签名测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   ObjectAnalyzer调用修复测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！文字识别调用已修复。")
        print("💡 现在可以运行main.py，文字识别应该能正常工作。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关代码。")

if __name__ == '__main__':
    main()
